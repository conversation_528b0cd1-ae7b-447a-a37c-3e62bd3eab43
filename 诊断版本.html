<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>三体世界 - 诊断版本</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #000;
            font-family: 'Courier New', monospace;
            color: #00ff88;
        }
        
        .status {
            background: rgba(0, 0, 0, 0.8);
            padding: 15px;
            border-radius: 10px;
            border: 1px solid #00ff88;
            margin-bottom: 20px;
        }
        
        .error {
            color: #ff4444;
            border-color: #ff4444;
        }
        
        .success {
            color: #44ff44;
            border-color: #44ff44;
        }
        
        .warning {
            color: #ffaa44;
            border-color: #ffaa44;
        }
        
        #canvas-container {
            border: 2px solid #333;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <h1>三体世界 - 系统诊断</h1>
    
    <div id="status-container">
        <div class="status" id="browser-info">
            <h3>浏览器信息</h3>
            <p>用户代理: <span id="user-agent"></span></p>
            <p>WebGL支持: <span id="webgl-support"></span></p>
        </div>
        
        <div class="status" id="p5-status">
            <h3>p5.js 加载状态</h3>
            <p>状态: <span id="p5-load-status">检查中...</span></p>
        </div>
        
        <div class="status" id="canvas-status">
            <h3>画布创建状态</h3>
            <p>状态: <span id="canvas-create-status">等待中...</span></p>
        </div>
        
        <div class="status" id="error-log">
            <h3>错误日志</h3>
            <div id="error-messages"></div>
        </div>
    </div>
    
    <div id="canvas-container">
        <!-- p5.js 画布将在这里创建 -->
    </div>

    <script>
        // 错误捕获
        var errorMessages = [];
        
        function logError(message) {
            errorMessages.push(new Date().toLocaleTimeString() + ": " + message);
            updateErrorDisplay();
            console.error(message);
        }
        
        function logSuccess(message) {
            console.log(message);
        }
        
        function updateErrorDisplay() {
            var errorDiv = document.getElementById('error-messages');
            if (errorMessages.length === 0) {
                errorDiv.innerHTML = '<span style="color: #44ff44;">无错误</span>';
            } else {
                errorDiv.innerHTML = errorMessages.map(function(msg) {
                    return '<div style="color: #ff4444;">' + msg + '</div>';
                }).join('');
            }
        }
        
        // 全局错误处理
        window.onerror = function(message, source, lineno, colno, error) {
            logError('JavaScript错误: ' + message + ' (行: ' + lineno + ')');
            return false;
        };
        
        // 检查浏览器信息
        function checkBrowserInfo() {
            document.getElementById('user-agent').textContent = navigator.userAgent;
            
            // 检查WebGL支持
            var canvas = document.createElement('canvas');
            var gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
            var webglSupport = !!gl;
            
            var webglElement = document.getElementById('webgl-support');
            if (webglSupport) {
                webglElement.textContent = '支持';
                webglElement.style.color = '#44ff44';
            } else {
                webglElement.textContent = '不支持';
                webglElement.style.color = '#ff4444';
                logError('WebGL不支持');
            }
        }
        
        // 页面加载完成后执行检查
        window.addEventListener('load', function() {
            logSuccess('页面加载完成');
            checkBrowserInfo();
            updateErrorDisplay();
            
            // 检查p5.js是否加载
            setTimeout(function() {
                if (typeof p5 !== 'undefined') {
                    document.getElementById('p5-load-status').textContent = '成功加载';
                    document.getElementById('p5-load-status').style.color = '#44ff44';
                    logSuccess('p5.js加载成功');
                    
                    // 尝试创建简单画布
                    setTimeout(initializeP5, 500);
                } else {
                    document.getElementById('p5-load-status').textContent = '加载失败';
                    document.getElementById('p5-load-status').style.color = '#ff4444';
                    logError('p5.js加载失败');
                    
                    // 提供本地备用方案
                    showFallbackOption();
                }
            }, 2000);
        });
        
        function initializeP5() {
            try {
                // 尝试创建2D画布
                new p5(function(p) {
                    p.setup = function() {
                        try {
                            var canvas = p.createCanvas(400, 300);
                            canvas.parent('canvas-container');
                            p.background(20, 20, 40);
                            
                            document.getElementById('canvas-create-status').textContent = '2D画布创建成功';
                            document.getElementById('canvas-create-status').style.color = '#44ff44';
                            logSuccess('2D画布创建成功');
                            
                            // 尝试创建3D画布
                            setTimeout(function() {
                                try3DCanvas(p);
                            }, 1000);
                            
                        } catch(error) {
                            logError('2D画布创建失败: ' + error.message);
                        }
                    };
                    
                    p.draw = function() {
                        try {
                            p.background(20, 20, 40);
                            p.fill(0, 255, 136);
                            p.textAlign(p.CENTER, p.CENTER);
                            p.text('2D模式正常工作', p.width/2, p.height/2);
                            
                            // 绘制一个简单的动画圆
                            p.fill(255, 100, 100);
                            p.ellipse(p.width/2 + p.cos(p.frameCount * 0.05) * 50, 
                                     p.height/2 + p.sin(p.frameCount * 0.05) * 30, 20, 20);
                        } catch(error) {
                            logError('绘制错误: ' + error.message);
                        }
                    };
                });
                
            } catch(error) {
                logError('p5.js初始化失败: ' + error.message);
            }
        }
        
        function try3DCanvas(p) {
            try {
                // 移除2D画布
                p.remove();
                
                // 创建3D画布
                new p5(function(p3d) {
                    p3d.setup = function() {
                        try {
                            var canvas3d = p3d.createCanvas(400, 300, p3d.WEBGL);
                            canvas3d.parent('canvas-container');
                            
                            document.getElementById('canvas-create-status').textContent = '3D画布创建成功！';
                            document.getElementById('canvas-create-status').style.color = '#44ff44';
                            logSuccess('3D画布创建成功');
                            
                            // 显示成功信息
                            setTimeout(function() {
                                showSuccessMessage();
                            }, 1000);
                            
                        } catch(error) {
                            logError('3D画布创建失败: ' + error.message);
                            document.getElementById('canvas-create-status').textContent = '3D不支持，使用2D模式';
                            document.getElementById('canvas-create-status').style.color = '#ffaa44';
                        }
                    };
                    
                    p3d.draw = function() {
                        try {
                            p3d.background(5, 5, 15);
                            p3d.ambientLight(50);
                            p3d.pointLight(255, 255, 255, 0, 0, 100);
                            
                            p3d.rotateY(p3d.frameCount * 0.01);
                            p3d.rotateX(p3d.frameCount * 0.005);
                            
                            // 绘制三个球体
                            p3d.push();
                            p3d.translate(-60, 0, 0);
                            p3d.fill(255, 100, 100);
                            p3d.sphere(15);
                            p3d.pop();
                            
                            p3d.push();
                            p3d.translate(60, 0, 0);
                            p3d.fill(100, 150, 255);
                            p3d.sphere(15);
                            p3d.pop();
                            
                            p3d.push();
                            p3d.translate(0, 60, 0);
                            p3d.fill(255, 255, 100);
                            p3d.sphere(15);
                            p3d.pop();
                            
                        } catch(error) {
                            logError('3D绘制错误: ' + error.message);
                        }
                    };
                });
                
            } catch(error) {
                logError('3D模式尝试失败: ' + error.message);
            }
        }
        
        function showSuccessMessage() {
            var container = document.getElementById('status-container');
            var successDiv = document.createElement('div');
            successDiv.className = 'status success';
            successDiv.innerHTML = '<h3>🎉 诊断完成</h3><p>您的系统支持3D渲染！现在可以运行完整的三体可视化了。</p>';
            container.appendChild(successDiv);
        }
        
        function showFallbackOption() {
            var container = document.getElementById('status-container');
            var fallbackDiv = document.createElement('div');
            fallbackDiv.className = 'status warning';
            fallbackDiv.innerHTML = '<h3>⚠️ 需要手动加载</h3><p>p5.js CDN加载失败，请检查网络连接或使用本地版本。</p>';
            container.appendChild(fallbackDiv);
        }
    </script>
    
    <!-- 尝试从CDN加载p5.js -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/p5.js/1.7.0/p5.min.js" 
            onerror="logError('p5.js CDN加载失败')"></script>
</body>
</html>
