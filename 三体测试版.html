<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>三体世界 - 测试版</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/p5.js/1.7.0/p5.min.js"></script>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: #000;
            font-family: 'Courier New', monospace;
            color: #00ff88;
            overflow: hidden;
        }
        
        .info {
            position: absolute;
            top: 20px;
            left: 20px;
            z-index: 100;
            background: rgba(0, 0, 0, 0.8);
            padding: 15px;
            border-radius: 10px;
            border: 1px solid #00ff88;
            box-shadow: 0 0 20px rgba(0, 255, 136, 0.3);
        }
        
        .title {
            position: absolute;
            top: 20px;
            right: 20px;
            z-index: 100;
            font-size: 24px;
            font-weight: bold;
            text-shadow: 0 0 10px rgba(0, 255, 136, 0.8);
        }
    </style>
</head>
<body>
    <div class="title">三体世界 - 测试版</div>
    
    <div class="info">
        <strong>测试版功能:</strong><br>
        • 三个恒星的基本运动<br>
        • 鼠标拖拽旋转视角<br>
        • 空格键暂停/继续<br>
        • 如果看到此信息说明页面加载成功
    </div>

    <script>
        // 全局变量
        let stars = [];
        let camera = {
            angleX: 0,
            angleY: 0,
            distance: 800
        };
        let timeSpeed = 1.0;
        let isPaused = false;
        
        // 物理常数
        const G = 0.3;
        const dt = 0.02;
        
        // 简化的恒星类
        class SimpleStar {
            constructor(x, y, z, mass, color, size) {
                this.pos = createVector(x, y, z);
                this.vel = createVector(0, 0, 0);
                this.mass = mass;
                this.color = color;
                this.size = size;
            }
            
            update(otherStars) {
                if (isPaused) return;
                
                let force = createVector(0, 0, 0);
                
                for (let other of otherStars) {
                    if (other !== this) {
                        let r = p5.Vector.sub(other.pos, this.pos);
                        let distance = r.mag();
                        distance = max(distance, 20);
                        
                        let forceMag = G * this.mass * other.mass / (distance * distance);
                        r.normalize();
                        r.mult(forceMag);
                        force.add(r);
                    }
                }
                
                let acc = p5.Vector.div(force, this.mass);
                this.vel.add(p5.Vector.mult(acc, dt * timeSpeed));
                this.pos.add(p5.Vector.mult(this.vel, dt * timeSpeed));
            }
            
            display() {
                push();
                translate(this.pos.x, this.pos.y, this.pos.z);
                
                // 简单的发光效果
                for (let i = 0; i < 3; i++) {
                    let alpha = map(i, 0, 2, 200, 50);
                    let size = this.size + i * 5;
                    fill(red(this.color), green(this.color), blue(this.color), alpha);
                    noStroke();
                    sphere(size);
                }
                
                pop();
            }
        }
        
        function setup() {
            try {
                console.log("开始初始化...");
                
                // 创建画布
                createCanvas(windowWidth, windowHeight, WEBGL);
                console.log("画布创建成功");
                
                // 初始化三个恒星
                stars.push(new SimpleStar(-150, 0, 0, 50, color(255, 100, 100), 20)); // 红色
                stars.push(new SimpleStar(150, 0, 0, 60, color(100, 150, 255), 22));  // 蓝色
                stars.push(new SimpleStar(0, 150, 0, 55, color(255, 255, 100), 21));  // 黄色
                
                // 给恒星初始速度
                stars[0].vel = createVector(0, 0.3, 0.1);
                stars[1].vel = createVector(0, -0.2, -0.3);
                stars[2].vel = createVector(0.3, -0.1, 0.1);
                
                console.log("恒星初始化完成");
                console.log("初始化成功！");
                
            } catch(error) {
                console.error("初始化错误:", error);
            }
        }
        
        function draw() {
            try {
                // 深色背景
                background(5, 5, 15);
                
                // 更新相机
                let x = camera.distance * cos(camera.angleY) * cos(camera.angleX);
                let y = camera.distance * sin(camera.angleX);
                let z = camera.distance * sin(camera.angleY) * cos(camera.angleX);
                camera(x, y, z, 0, 0, 0, 0, 1, 0);
                
                // 基本光照
                ambientLight(50, 50, 70);
                pointLight(255, 255, 255, 0, 0, 0);
                
                // 绘制坐标轴（帮助调试）
                strokeWeight(2);
                stroke(255, 0, 0); line(0, 0, 0, 100, 0, 0); // X轴
                stroke(0, 255, 0); line(0, 0, 0, 0, 100, 0); // Y轴
                stroke(0, 0, 255); line(0, 0, 0, 0, 0, 100); // Z轴
                
                // 更新和绘制恒星
                for (let star of stars) {
                    star.update(stars);
                    star.display();
                }
                
                // 显示状态信息
                if (frameCount % 60 === 0) {
                    console.log("运行正常，FPS:", frameRate().toFixed(1));
                }
                
            } catch(error) {
                console.error("绘制错误:", error);
                background(50, 0, 0);
                fill(255);
                textAlign(CENTER, CENTER);
                text("发生错误，请查看控制台", 0, 0);
            }
        }
        
        // 鼠标控制
        function mouseDragged() {
            camera.angleY += (mouseX - pmouseX) * 0.01;
            camera.angleX -= (mouseY - pmouseY) * 0.01;
            camera.angleX = constrain(camera.angleX, -PI/2, PI/2);
        }
        
        function mouseWheel(event) {
            camera.distance += event.delta * 2;
            camera.distance = constrain(camera.distance, 200, 2000);
            return false;
        }
        
        // 键盘控制
        function keyPressed() {
            if (key === ' ') {
                isPaused = !isPaused;
                console.log(isPaused ? "已暂停" : "继续运行");
            }
        }
        
        function windowResized() {
            resizeCanvas(windowWidth, windowHeight);
        }
        
        // 页面加载完成后的检查
        window.addEventListener('load', function() {
            console.log("页面加载完成");
            setTimeout(function() {
                if (typeof setup === 'function') {
                    console.log("p5.js 函数可用");
                } else {
                    console.error("p5.js 未正确加载");
                }
            }, 1000);
        });
    </script>
</body>
</html>
