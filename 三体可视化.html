<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>三体世界 - 混沌星系可视化</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/p5.js/1.7.0/p5.min.js"></script>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: #000;
            font-family: 'Courier New', monospace;
            color: #00ff88;
            overflow: hidden;
        }

        .controls {
            position: absolute;
            top: 20px;
            left: 20px;
            z-index: 100;
            background: rgba(0, 0, 0, 0.8);
            padding: 15px;
            border-radius: 10px;
            border: 1px solid #00ff88;
            box-shadow: 0 0 20px rgba(0, 255, 136, 0.3);
        }

        .control-item {
            margin: 10px 0;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .control-item label {
            min-width: 80px;
            font-size: 12px;
        }

        input[type="range"] {
            background: #333;
            outline: none;
            height: 5px;
            border-radius: 5px;
        }

        input[type="range"]::-webkit-slider-thumb {
            appearance: none;
            width: 15px;
            height: 15px;
            border-radius: 50%;
            background: #00ff88;
            cursor: pointer;
            box-shadow: 0 0 10px rgba(0, 255, 136, 0.5);
        }

        .info {
            position: absolute;
            bottom: 20px;
            left: 20px;
            z-index: 100;
            background: rgba(0, 0, 0, 0.8);
            padding: 10px;
            border-radius: 5px;
            border: 1px solid #00ff88;
            font-size: 11px;
            max-width: 300px;
        }

        .title {
            position: absolute;
            top: 20px;
            right: 20px;
            z-index: 100;
            font-size: 24px;
            font-weight: bold;
            text-shadow: 0 0 10px rgba(0, 255, 136, 0.8);
        }
    </style>
</head>
<body>
    <div class="title">三体世界</div>

    <div class="controls">
        <div class="control-item">
            <label>时间流速:</label>
            <input type="range" id="timeSpeed" min="0.1" max="3" step="0.1" value="1">
            <span id="speedValue">1.0x</span>
        </div>
        <div class="control-item">
            <label>视角距离:</label>
            <input type="range" id="cameraDistance" min="500" max="2000" step="50" value="1000">
        </div>
        <div class="control-item">
            <label>粒子密度:</label>
            <input type="range" id="particleDensity" min="100" max="1000" step="50" value="500">
        </div>
    </div>

    <div class="info">
        <strong>操作说明:</strong><br>
        • 鼠标拖拽: 旋转视角<br>
        • 鼠标滚轮: 缩放视距<br>
        • 空格键: 暂停/继续<br>
        • R键: 重置系统<br><br>
        <strong>三体系统:</strong><br>
        红色恒星、蓝色恒星、黄色恒星<br>
        绿色行星在混沌轨道中运行
    </div>

    <script>
        // 全局变量
        let stars = [];
        let planet;
        let particles = [];
        let camera = {
            angleX: 0,
            angleY: 0,
            distance: 1000,
            targetDistance: 1000
        };
        let timeSpeed = 1.0;
        let isPaused = false;
        let starField = [];

        // 物理常数
        const G = 0.5; // 引力常数
        const dt = 0.02; // 时间步长

        // 恒星类
        class Star {
            constructor(x, y, z, mass, color, size) {
                this.pos = createVector(x, y, z);
                this.vel = createVector(0, 0, 0);
                this.mass = mass;
                this.color = color;
                this.size = size;
                this.trail = [];
                this.glowParticles = [];
            }

            update(otherStars) {
                if (isPaused) return;

                let force = createVector(0, 0, 0);

                // 计算其他恒星的引力
                for (let other of otherStars) {
                    if (other !== this) {
                        let r = p5.Vector.sub(other.pos, this.pos);
                        let distance = r.mag();
                        distance = max(distance, 10); // 避免奇点

                        let forceMag = G * this.mass * other.mass / (distance * distance);
                        r.normalize();
                        r.mult(forceMag);
                        force.add(r);
                    }
                }

                // 更新速度和位置
                let acc = p5.Vector.div(force, this.mass);
                this.vel.add(p5.Vector.mult(acc, dt * timeSpeed));
                this.pos.add(p5.Vector.mult(this.vel, dt * timeSpeed));

                // 更新轨迹
                this.trail.push(this.pos.copy());
                if (this.trail.length > 200) {
                    this.trail.shift();
                }

                // 更新发光粒子
                this.updateGlowParticles();
            }

            updateGlowParticles() {
                // 添加新的发光粒子
                if (frameCount % 3 === 0) {
                    for (let i = 0; i < 3; i++) {
                        this.glowParticles.push({
                            pos: this.pos.copy(),
                            vel: p5.Vector.random3D().mult(random(1, 3)),
                            life: 1.0,
                            size: random(2, 6)
                        });
                    }
                }

                // 更新粒子
                for (let i = this.glowParticles.length - 1; i >= 0; i--) {
                    let p = this.glowParticles[i];
                    p.pos.add(p.vel);
                    p.life -= 0.02;
                    p.size *= 0.98;

                    if (p.life <= 0) {
                        this.glowParticles.splice(i, 1);
                    }
                }
            }

            display() {
                push();
                translate(this.pos.x, this.pos.y, this.pos.z);

                // 绘制发光效果
                for (let i = 0; i < 5; i++) {
                    let alpha = map(i, 0, 4, 100, 10);
                    let size = this.size + i * 8;
                    fill(red(this.color), green(this.color), blue(this.color), alpha);
                    noStroke();
                    sphere(size);
                }

                // 绘制恒星本体
                fill(this.color);
                noStroke();
                sphere(this.size);

                pop();

                // 绘制轨迹
                this.drawTrail();

                // 绘制发光粒子
                this.drawGlowParticles();
            }

            drawTrail() {
                if (this.trail.length < 2) return;

                stroke(red(this.color), green(this.color), blue(this.color), 100);
                strokeWeight(1);
                noFill();

                beginShape();
                for (let i = 0; i < this.trail.length; i++) {
                    let alpha = map(i, 0, this.trail.length - 1, 0, 100);
                    stroke(red(this.color), green(this.color), blue(this.color), alpha);
                    vertex(this.trail[i].x, this.trail[i].y, this.trail[i].z);
                }
                endShape();
            }

            drawGlowParticles() {
                for (let p of this.glowParticles) {
                    push();
                    translate(p.pos.x, p.pos.y, p.pos.z);
                    let alpha = p.life * 150;
                    fill(red(this.color), green(this.color), blue(this.color), alpha);
                    noStroke();
                    sphere(p.size);
                    pop();
                }
            }
        }

        // 行星类
        class Planet {
            constructor(x, y, z) {
                this.pos = createVector(x, y, z);
                this.vel = createVector(0, 0, 0);
                this.mass = 1;
                this.size = 8;
                this.color = color(0, 255, 100);
                this.trail = [];
            }

            update(stars) {
                if (isPaused) return;

                let force = createVector(0, 0, 0);

                // 计算所有恒星的引力
                for (let star of stars) {
                    let r = p5.Vector.sub(star.pos, this.pos);
                    let distance = r.mag();
                    distance = max(distance, 10);

                    let forceMag = G * this.mass * star.mass / (distance * distance);
                    r.normalize();
                    r.mult(forceMag);
                    force.add(r);
                }

                // 更新速度和位置
                let acc = p5.Vector.div(force, this.mass);
                this.vel.add(p5.Vector.mult(acc, dt * timeSpeed));
                this.pos.add(p5.Vector.mult(this.vel, dt * timeSpeed));

                // 更新轨迹
                this.trail.push(this.pos.copy());
                if (this.trail.length > 500) {
                    this.trail.shift();
                }
            }

            display() {
                // 绘制轨迹
                if (this.trail.length > 1) {
                    stroke(0, 255, 100, 80);
                    strokeWeight(1);
                    noFill();

                    beginShape();
                    for (let i = 0; i < this.trail.length; i++) {
                        let alpha = map(i, 0, this.trail.length - 1, 0, 80);
                        stroke(0, 255, 100, alpha);
                        vertex(this.trail[i].x, this.trail[i].y, this.trail[i].z);
                    }
                    endShape();
                }

                // 绘制行星
                push();
                translate(this.pos.x, this.pos.y, this.pos.z);

                // 发光效果
                for (let i = 0; i < 3; i++) {
                    let alpha = map(i, 0, 2, 150, 30);
                    let size = this.size + i * 4;
                    fill(0, 255, 100, alpha);
                    noStroke();
                    sphere(size);
                }

                // 行星本体
                fill(this.color);
                noStroke();
                sphere(this.size);

                pop();
            }
        }

        // 粒子类
        class Particle {
            constructor() {
                this.pos = createVector(
                    random(-2000, 2000),
                    random(-2000, 2000),
                    random(-2000, 2000)
                );
                this.vel = p5.Vector.random3D().mult(random(0.1, 0.5));
                this.size = random(1, 3);
                this.brightness = random(50, 255);
                this.twinkle = random(0, TWO_PI);
            }

            update() {
                if (!isPaused) {
                    this.pos.add(this.vel);
                    this.twinkle += 0.05;

                    // 边界检查
                    if (this.pos.x > 2000) this.pos.x = -2000;
                    if (this.pos.x < -2000) this.pos.x = 2000;
                    if (this.pos.y > 2000) this.pos.y = -2000;
                    if (this.pos.y < -2000) this.pos.y = 2000;
                    if (this.pos.z > 2000) this.pos.z = -2000;
                    if (this.pos.z < -2000) this.pos.z = 2000;
                }
            }

            display() {
                push();
                translate(this.pos.x, this.pos.y, this.pos.z);
                let alpha = this.brightness + sin(this.twinkle) * 50;
                fill(255, 255, 255, alpha);
                noStroke();
                sphere(this.size);
                pop();
            }
        }

        // 初始化星空背景
        function createStarField() {
            starField = [];
            for (let i = 0; i < 200; i++) {
                starField.push({
                    x: random(-3000, 3000),
                    y: random(-3000, 3000),
                    z: random(-3000, 3000),
                    size: random(1, 4),
                    brightness: random(100, 255)
                });
            }
        }

        // 绘制星空背景
        function drawStarField() {
            for (let star of starField) {
                push();
                translate(star.x, star.y, star.z);
                fill(255, 255, 255, star.brightness);
                noStroke();
                sphere(star.size);
                pop();
            }
        }

        // p5.js 主函数
        function setup() {
            createCanvas(windowWidth, windowHeight, WEBGL);

            // 初始化三个恒星
            stars.push(new Star(-200, 0, 0, 100, color(255, 100, 100), 25)); // 红色恒星
            stars.push(new Star(200, 0, 0, 120, color(100, 150, 255), 28));  // 蓝色恒星
            stars.push(new Star(0, 200, 0, 110, color(255, 255, 100), 26));  // 黄色恒星

            // 给恒星初始速度
            stars[0].vel = createVector(0, 0.5, 0.2);
            stars[1].vel = createVector(0, -0.3, -0.4);
            stars[2].vel = createVector(0.4, -0.2, 0.1);

            // 初始化行星
            planet = new Planet(100, -100, 50);
            planet.vel = createVector(0.8, 0.6, 0.3);

            // 初始化粒子
            let particleCount = parseInt(document.getElementById('particleDensity').value);
            for (let i = 0; i < particleCount; i++) {
                particles.push(new Particle());
            }

            // 初始化星空背景
            createStarField();

            // 设置控制器事件
            setupControls();
        }

        function draw() {
            background(5, 5, 15);

            // 更新相机
            updateCamera();

            // 设置光照
            ambientLight(30, 30, 50);
            pointLight(255, 255, 255, 0, 0, 0);

            // 绘制星空背景
            drawStarField();

            // 更新和绘制粒子
            for (let particle of particles) {
                particle.update();
                particle.display();
            }

            // 更新和绘制恒星
            for (let star of stars) {
                star.update(stars);
                star.display();
            }

            // 更新和绘制行星
            planet.update(stars);
            planet.display();
        }

        // 相机控制
        function updateCamera() {
            camera.distance = lerp(camera.distance, camera.targetDistance, 0.05);

            let x = camera.distance * cos(camera.angleY) * cos(camera.angleX);
            let y = camera.distance * sin(camera.angleX);
            let z = camera.distance * sin(camera.angleY) * cos(camera.angleX);

            camera(x, y, z, 0, 0, 0, 0, 1, 0);
        }

        // 控制器设置
        function setupControls() {
            // 时间流速控制
            document.getElementById('timeSpeed').addEventListener('input', function(e) {
                timeSpeed = parseFloat(e.target.value);
                document.getElementById('speedValue').textContent = timeSpeed.toFixed(1) + 'x';
            });

            // 相机距离控制
            document.getElementById('cameraDistance').addEventListener('input', function(e) {
                camera.targetDistance = parseInt(e.target.value);
            });

            // 粒子密度控制
            document.getElementById('particleDensity').addEventListener('input', function(e) {
                let newCount = parseInt(e.target.value);
                let currentCount = particles.length;

                if (newCount > currentCount) {
                    for (let i = 0; i < newCount - currentCount; i++) {
                        particles.push(new Particle());
                    }
                } else if (newCount < currentCount) {
                    particles.splice(newCount);
                }
            });
        }

        // 鼠标交互
        function mouseDragged() {
            if (mouseX > 0 && mouseX < width && mouseY > 0 && mouseY < height) {
                camera.angleY += (mouseX - pmouseX) * 0.01;
                camera.angleX -= (mouseY - pmouseY) * 0.01;
                camera.angleX = constrain(camera.angleX, -PI/2, PI/2);
            }
        }

        function mouseWheel(event) {
            camera.targetDistance += event.delta * 2;
            camera.targetDistance = constrain(camera.targetDistance, 200, 3000);
            return false;
        }

        // 键盘控制
        function keyPressed() {
            if (key === ' ') {
                isPaused = !isPaused;
            } else if (key === 'r' || key === 'R') {
                resetSystem();
            }
        }

        // 重置系统
        function resetSystem() {
            // 重置恒星位置和速度
            stars[0].pos = createVector(-200, 0, 0);
            stars[0].vel = createVector(0, 0.5, 0.2);
            stars[0].trail = [];

            stars[1].pos = createVector(200, 0, 0);
            stars[1].vel = createVector(0, -0.3, -0.4);
            stars[1].trail = [];

            stars[2].pos = createVector(0, 200, 0);
            stars[2].vel = createVector(0.4, -0.2, 0.1);
            stars[2].trail = [];

            // 重置行星
            planet.pos = createVector(100, -100, 50);
            planet.vel = createVector(0.8, 0.6, 0.3);
            planet.trail = [];

            // 清除发光粒子
            for (let star of stars) {
                star.glowParticles = [];
            }
        }

        // 窗口大小调整
        function windowResized() {
            resizeCanvas(windowWidth, windowHeight);
        }
    </script>
</body>
</html>