<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>三体世界 - 三维可视化</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/p5.js/1.7.0/p5.min.js"></script>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: #000;
            font-family: 'Courier New', monospace;
            overflow: hidden;
        }
        
        .control-panel {
            position: absolute;
            top: 20px;
            left: 20px;
            background: rgba(0, 50, 100, 0.8);
            color: #00ffff;
            padding: 15px;
            border-radius: 10px;
            border: 1px solid #00ffff;
            z-index: 1000;
            font-size: 12px;
            min-width: 200px;
        }
        
        .control-panel h3 {
            margin: 0 0 10px 0;
            color: #ffff00;
            text-align: center;
        }
        
        .control-item {
            margin: 8px 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .control-item label {
            color: #00ffff;
        }
        
        .control-item input, .control-item button {
            background: rgba(0, 100, 200, 0.5);
            border: 1px solid #00ffff;
            color: #ffffff;
            padding: 3px 8px;
            border-radius: 3px;
        }
        
        .control-item button {
            cursor: pointer;
            transition: background 0.3s;
        }
        
        .control-item button:hover {
            background: rgba(0, 150, 255, 0.8);
        }
        
        .info-panel {
            position: absolute;
            top: 20px;
            right: 20px;
            background: rgba(100, 0, 0, 0.8);
            color: #ff6666;
            padding: 15px;
            border-radius: 10px;
            border: 1px solid #ff6666;
            z-index: 1000;
            font-size: 11px;
            max-width: 250px;
        }
        
        .info-panel h4 {
            margin: 0 0 8px 0;
            color: #ffff00;
        }
    </style>
</head>
<body>
    <div class="control-panel">
        <h3>三体控制系统</h3>
        <div class="control-item">
            <label>时间流速:</label>
            <input type="range" id="timeSpeed" min="0" max="3" step="0.1" value="1">
        </div>
        <div class="control-item">
            <label>显示轨迹:</label>
            <button id="toggleTrails" onclick="toggleTrails()">开启</button>
        </div>
        <div class="control-item">
            <label>粒子效果:</label>
            <button id="toggleParticles" onclick="toggleParticles()">开启</button>
        </div>
        <div class="control-item">
            <label>重置视角:</label>
            <button onclick="resetCamera()">重置</button>
        </div>
        <div class="control-item">
            <label>暂停/继续:</label>
            <button id="pauseBtn" onclick="togglePause()">暂停</button>
        </div>
    </div>
    
    <div class="info-panel">
        <h4>操作说明</h4>
        <p>• 鼠标拖拽: 旋转视角</p>
        <p>• 滚轮: 缩放</p>
        <p>• 空格键: 暂停/继续</p>
        <p>• R键: 重置视角</p>
        <br>
        <h4>三体状态</h4>
        <p id="systemInfo">正在计算...</p>
    </div>

    <script>
        // 全局变量
        let stars = [];
        let planet;
        let particles = [];
        let starTrails = [];
        let planetTrail = [];
        
        // 控制变量
        let timeScale = 1.0;
        let showTrails = true;
        let showParticles = true;
        let isPaused = false;
        
        // 相机控制
        let cameraAngleX = 0;
        let cameraAngleY = 0;
        let cameraDistance = 800;
        let lastMouseX, lastMouseY;
        let isDragging = false;
        
        // 物理常量
        const G = 0.5; // 引力常数（调整后的值）
        const MAX_TRAIL_LENGTH = 200;
        
        function setup() {
            createCanvas(windowWidth, windowHeight, WEBGL);
            
            // 初始化三个恒星
            stars = [
                createStar(-100, 0, 0, 50, [255, 100, 100], 25), // 红色恒星
                createStar(100, 0, 0, 45, [100, 255, 100], 22),  // 绿色恒星
                createStar(0, 0, 150, 40, [100, 100, 255], 20)   // 蓝色恒星
            ];
            
            // 给恒星初始速度（形成混沌运动）
            stars[0].velocity = createVector(0, 1, 0.5);
            stars[1].velocity = createVector(0, -0.8, -0.3);
            stars[2].velocity = createVector(-0.5, 0.2, 0);
            
            // 初始化行星
            planet = {
                position: createVector(200, 50, 0),
                velocity: createVector(0, 0, 2),
                mass: 1,
                size: 8,
                color: [200, 200, 255]
            };
            
            // 初始化粒子系统
            for (let i = 0; i < 300; i++) {
                particles.push(createParticle());
            }
            
            // 初始化轨迹数组
            for (let i = 0; i < stars.length; i++) {
                starTrails[i] = [];
            }
        }
        
        function createStar(x, y, z, mass, color, size) {
            return {
                position: createVector(x, y, z),
                velocity: createVector(0, 0, 0),
                mass: mass,
                size: size,
                color: color,
                glowIntensity: 1.0
            };
        }
        
        function createParticle() {
            return {
                position: createVector(
                    random(-1000, 1000),
                    random(-1000, 1000),
                    random(-1000, 1000)
                ),
                velocity: createVector(
                    random(-0.1, 0.1),
                    random(-0.1, 0.1),
                    random(-0.1, 0.1)
                ),
                life: random(100, 300),
                maxLife: random(100, 300),
                size: random(1, 3)
            };
        }
        
        function draw() {
            background(5, 5, 20);
            
            // 更新相机
            translate(0, 0, -cameraDistance);
            rotateX(cameraAngleX);
            rotateY(cameraAngleY);
            
            if (!isPaused) {
                // 更新物理系统
                updatePhysics();
                
                // 更新粒子
                if (showParticles) {
                    updateParticles();
                }
                
                // 记录轨迹
                if (showTrails) {
                    recordTrails();
                }
                
                // 更新信息面板
                updateSystemInfo();
            }
            
            // 渲染星空背景
            drawStarField();
            
            // 渲染轨迹
            if (showTrails) {
                drawTrails();
            }
            
            // 渲染恒星
            drawStars();
            
            // 渲染行星
            drawPlanet();
            
            // 渲染粒子效果
            if (showParticles) {
                drawParticles();
            }
            
            // 渲染中心坐标系
            drawCoordinateSystem();
        }
        
        function updatePhysics() {
            let dt = timeScale * 0.016; // 时间步长
            
            // 计算恒星间的引力
            for (let i = 0; i < stars.length; i++) {
                let star1 = stars[i];
                star1.acceleration = createVector(0, 0, 0);
                
                // 与其他恒星的引力
                for (let j = 0; j < stars.length; j++) {
                    if (i !== j) {
                        let star2 = stars[j];
                        let force = calculateGravity(star1, star2);
                        star1.acceleration.add(force.div(star1.mass));
                    }
                }
                
                // 更新速度和位置
                star1.velocity.add(p5.Vector.mult(star1.acceleration, dt));
                star1.position.add(p5.Vector.mult(star1.velocity, dt));
                
                // 添加微小的随机扰动（模拟三体问题的混沌性）
                star1.velocity.add(createVector(
                    random(-0.005, 0.005),
                    random(-0.005, 0.005),
                    random(-0.005, 0.005)
                ));
            }
            
            // 计算行星受到的引力
            planet.acceleration = createVector(0, 0, 0);
            for (let star of stars) {
                let force = calculateGravity(planet, star);
                planet.acceleration.add(force.div(planet.mass));
            }
            
            // 更新行星运动
            planet.velocity.add(p5.Vector.mult(planet.acceleration, dt));
            planet.position.add(p5.Vector.mult(planet.velocity, dt));
        }
        
        function calculateGravity(obj1, obj2) {
            let force = p5.Vector.sub(obj2.position, obj1.position);
            let distance = force.mag();
            distance = constrain(distance, 10, 1000); // 防止距离过小导致计算错误
            
            let strength = (G * obj1.mass * obj2.mass) / (distance * distance);
            force.setMag(strength);
            
            return force;
        }
        
        function updateParticles() {
            for (let i = particles.length - 1; i >= 0; i--) {
                let p = particles[i];
                
                // 更新位置
                p.position.add(p.velocity);
                
                // 减少生命值
                p.life--;
                
                // 添加微弱的引力影响
                for (let star of stars) {
                    let dist = p5.Vector.dist(p.position, star.position);
                    if (dist < 200) {
                        let pull = p5.Vector.sub(star.position, p.position);
                        pull.setMag(0.001);
                        p.velocity.add(pull);
                    }
                }
                
                // 重生粒子
                if (p.life <= 0) {
                    particles[i] = createParticle();
                }
            }
        }
        
        function recordTrails() {
            // 记录恒星轨迹
            for (let i = 0; i < stars.length; i++) {
                starTrails[i].push(stars[i].position.copy());
                if (starTrails[i].length > MAX_TRAIL_LENGTH) {
                    starTrails[i].shift();
                }
            }
            
            // 记录行星轨迹
            planetTrail.push(planet.position.copy());
            if (planetTrail.length > MAX_TRAIL_LENGTH * 2) {
                planetTrail.shift();
            }
        }
        
        function drawStarField() {
            // 绘制远景星空
            push();
            noFill();
            for (let i = 0; i < 100; i++) {
                let angle1 = random(TWO_PI);
                let angle2 = random(TWO_PI);
                let radius = 2000;
                
                let x = radius * cos(angle1) * cos(angle2);
                let y = radius * sin(angle1) * cos(angle2);
                let z = radius * sin(angle2);
                
                push();
                translate(x, y, z);
                fill(255, 255, 255, random(50, 150));
                noStroke();
                sphere(random(1, 2));
                pop();
            }
            pop();
        }
        
        function drawTrails() {
            // 绘制恒星轨迹
            for (let i = 0; i < starTrails.length; i++) {
                let trail = starTrails[i];
                let star = stars[i];
                
                if (trail.length > 1) {
                    stroke(star.color[0], star.color[1], star.color[2], 100);
                    strokeWeight(2);
                    noFill();
                    
                    beginShape();
                    for (let j = 0; j < trail.length; j++) {
                        let alpha = map(j, 0, trail.length - 1, 10, 100);
                        stroke(star.color[0], star.color[1], star.color[2], alpha);
                        vertex(trail[j].x, trail[j].y, trail[j].z);
                    }
                    endShape();
                }
            }
            
            // 绘制行星轨迹
            if (planetTrail.length > 1) {
                stroke(planet.color[0], planet.color[1], planet.color[2], 80);
                strokeWeight(1);
                noFill();
                
                beginShape();
                for (let i = 0; i < planetTrail.length; i++) {
                    let alpha = map(i, 0, planetTrail.length - 1, 10, 80);
                    stroke(planet.color[0], planet.color[1], planet.color[2], alpha);
                    vertex(planetTrail[i].x, planetTrail[i].y, planetTrail[i].z);
                }
                endShape();
            }
        }
        
        function drawStars() {
            for (let star of stars) {
                push();
                translate(star.position.x, star.position.y, star.position.z);
                
                // 恒星光晕效果
                for (let i = 3; i >= 0; i--) {
                    let glowSize = star.size + i * 8;
                    let alpha = map(i, 0, 3, 255, 30);
                    
                    fill(star.color[0], star.color[1], star.color[2], alpha);
                    noStroke();
                    sphere(glowSize);
                }
                
                // 恒星核心
                fill(255, 255, 255, 200);
                noStroke();
                sphere(star.size * 0.6);
                
                // 添加闪烁效果
                if (frameCount % 60 < 30) {
                    fill(255, 255, 255, 100);
                    sphere(star.size * 1.2);
                }
                
                pop();
            }
        }
        
        function drawPlanet() {
            push();
            translate(planet.position.x, planet.position.y, planet.position.z);
            
            // 行星表面
            fill(planet.color[0], planet.color[1], planet.color[2]);
            noStroke();
            sphere(planet.size);
            
            // 行星大气层效果
            fill(planet.color[0], planet.color[1], planet.color[2], 50);
            sphere(planet.size * 1.3);
            
            pop();
        }
        
        function drawParticles() {
            for (let p of particles) {
                push();
                translate(p.position.x, p.position.y, p.position.z);
                
                let alpha = map(p.life, 0, p.maxLife, 0, 100);
                fill(150, 150, 255, alpha);
                noStroke();
                sphere(p.size);
                
                pop();
            }
        }
        
        function drawCoordinateSystem() {
            // 绘制坐标轴
            strokeWeight(1);
            
            // X轴 - 红色
            stroke(255, 0, 0, 100);
            line(-100, 0, 0, 100, 0, 0);
            
            // Y轴 - 绿色
            stroke(0, 255, 0, 100);
            line(0, -100, 0, 0, 100, 0);
            
            // Z轴 - 蓝色
            stroke(0, 0, 255, 100);
            line(0, 0, -100, 0, 0, 100);
        }
        
        function updateSystemInfo() {
            if (frameCount % 30 === 0) { // 每30帧更新一次
                let info = "恒星距离:\n";
                for (let i = 0; i < stars.length; i++) {
                    for (let j = i + 1; j < stars.length; j++) {
                        let dist = p5.Vector.dist(stars[i].position, stars[j].position);
                        info += `恒星${i+1}-${j+1}: ${dist.toFixed(1)}\n`;
                    }
                }
                
                let planetDist = 0;
                for (let star of stars) {
                    planetDist += p5.Vector.dist(planet.position, star.position);
                }
                planetDist /= stars.length;
                info += `行星平均距离: ${planetDist.toFixed(1)}`;
                
                document.getElementById('systemInfo').innerText = info;
            }
        }
        
        // 鼠标控制
        function mousePressed() {
            isDragging = true;
            lastMouseX = mouseX;
            lastMouseY = mouseY;
        }
        
        function mouseReleased() {
            isDragging = false;
        }
        
        function mouseDragged() {
            if (isDragging) {
                let deltaX = mouseX - lastMouseX;
                let deltaY = mouseY - lastMouseY;
                
                cameraAngleY += deltaX * 0.01;
                cameraAngleX += deltaY * 0.01;
                
                cameraAngleX = constrain(cameraAngleX, -PI/2, PI/2);
                
                lastMouseX = mouseX;
                lastMouseY = mouseY;
            }
        }
        
        function mouseWheel(event) {
            cameraDistance += event.delta * 2;
            cameraDistance = constrain(cameraDistance, 200, 2000);
            return false;
        }
        
        // 键盘控制
        function keyPressed() {
            if (key === ' ') {
                togglePause();
            } else if (key === 'r' || key === 'R') {
                resetCamera();
            }
        }
        
        // 控制面板函数
        function toggleTrails() {
            showTrails = !showTrails;
            document.getElementById('toggleTrails').innerText = showTrails ? '关闭' : '开启';
            
            if (!showTrails) {
                // 清空轨迹
                for (let i = 0; i < starTrails.length; i++) {
                    starTrails[i] = [];
                }
                planetTrail = [];
            }
        }
        
        function toggleParticles() {
            showParticles = !showParticles;
            document.getElementById('toggleParticles').innerText = showParticles ? '关闭' : '开启';
        }
        
        function togglePause() {
            isPaused = !isPaused;
            document.getElementById('pauseBtn').innerText = isPaused ? '继续' : '暂停';
        }
        
        function resetCamera() {
            cameraAngleX = 0;
            cameraAngleY = 0;
            cameraDistance = 800;
        }
        
        // 时间流速控制
        document.getElementById('timeSpeed').addEventListener('input', function(e) {
            timeScale = parseFloat(e.target.value);
        });
        
        function windowResized() {
            resizeCanvas(windowWidth, windowHeight);
        }
    </script>
</body>
</html>