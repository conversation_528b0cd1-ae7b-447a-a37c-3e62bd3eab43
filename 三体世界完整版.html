<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>三体世界 - 混沌星系</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/p5.js/1.7.0/p5.min.js"></script>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: #000;
            font-family: 'Courier New', monospace;
            color: #00ff88;
            overflow: hidden;
        }

        .controls {
            position: absolute;
            top: 20px;
            left: 20px;
            z-index: 100;
            background: rgba(0, 20, 40, 0.9);
            padding: 20px;
            border-radius: 15px;
            border: 2px solid #00ff88;
            box-shadow: 0 0 30px rgba(0, 255, 136, 0.4);
            backdrop-filter: blur(10px);
            min-width: 250px;
        }

        .control-group {
            margin: 15px 0;
        }

        .control-group label {
            display: block;
            margin-bottom: 8px;
            font-size: 14px;
            font-weight: bold;
            text-shadow: 0 0 5px rgba(0, 255, 136, 0.8);
        }

        .slider-container {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        input[type="range"] {
            flex: 1;
            height: 6px;
            background: linear-gradient(to right, #003366, #00ff88);
            border-radius: 3px;
            outline: none;
            -webkit-appearance: none;
        }

        input[type="range"]::-webkit-slider-thumb {
            -webkit-appearance: none;
            width: 18px;
            height: 18px;
            border-radius: 50%;
            background: radial-gradient(circle, #00ff88, #004466);
            cursor: pointer;
            box-shadow: 0 0 15px rgba(0, 255, 136, 0.8);
            border: 2px solid #00ff88;
        }

        .value-display {
            min-width: 60px;
            text-align: center;
            background: rgba(0, 255, 136, 0.1);
            padding: 4px 8px;
            border-radius: 5px;
            border: 1px solid #00ff88;
            font-size: 12px;
        }

        .info-panel {
            position: absolute;
            bottom: 20px;
            left: 20px;
            z-index: 100;
            background: rgba(0, 20, 40, 0.9);
            padding: 15px;
            border-radius: 10px;
            border: 1px solid #00ff88;
            backdrop-filter: blur(10px);
            max-width: 300px;
        }

        .title {
            position: absolute;
            top: 20px;
            right: 20px;
            z-index: 100;
            font-size: 28px;
            font-weight: bold;
            text-shadow: 0 0 20px rgba(0, 255, 136, 1);
            background: linear-gradient(45deg, #00ff88, #0088ff);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .status-indicator {
            position: absolute;
            top: 80px;
            right: 20px;
            z-index: 100;
            padding: 10px;
            background: rgba(0, 20, 40, 0.8);
            border-radius: 8px;
            border: 1px solid #00ff88;
            font-size: 12px;
        }

        .button {
            background: linear-gradient(45deg, #003366, #00ff88);
            border: none;
            color: white;
            padding: 8px 16px;
            border-radius: 5px;
            cursor: pointer;
            font-family: inherit;
            font-size: 12px;
            margin: 5px;
            transition: all 0.3s;
        }

        .button:hover {
            box-shadow: 0 0 15px rgba(0, 255, 136, 0.6);
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <div class="title">三体世界</div>

    <div class="status-indicator">
        <div>FPS: <span id="fps-display">--</span></div>
        <div>状态: <span id="status-display">运行中</span></div>
    </div>

    <div class="controls">
        <h3 style="margin-top: 0; text-align: center;">控制面板</h3>

        <div class="control-group">
            <label>时间流速</label>
            <div class="slider-container">
                <input type="range" id="timeSpeed" min="0" max="3" step="0.1" value="1">
                <div class="value-display" id="timeSpeedValue">1.0x</div>
            </div>
        </div>

        <div class="control-group">
            <label>视角距离</label>
            <div class="slider-container">
                <input type="range" id="cameraDistance" min="300" max="2000" step="50" value="800">
                <div class="value-display" id="cameraDistanceValue">800</div>
            </div>
        </div>

        <div class="control-group">
            <label>粒子密度</label>
            <div class="slider-container">
                <input type="range" id="particleCount" min="50" max="500" step="25" value="200">
                <div class="value-display" id="particleCountValue">200</div>
            </div>
        </div>

        <div class="control-group">
            <label>恒星发光强度</label>
            <div class="slider-container">
                <input type="range" id="glowIntensity" min="0.5" max="3" step="0.1" value="1.5">
                <div class="value-display" id="glowIntensityValue">1.5</div>
            </div>
        </div>

        <div class="control-group" style="text-align: center;">
            <button class="button" onclick="togglePause()">暂停/继续</button>
            <button class="button" onclick="resetSystem()">重置系统</button>
        </div>
    </div>

    <div class="info-panel">
        <h4 style="margin-top: 0;">操作指南</h4>
        <p><strong>鼠标操作:</strong></p>
        <p>• 拖拽: 旋转视角</p>
        <p>• 滚轮: 缩放距离</p>
        <p><strong>键盘操作:</strong></p>
        <p>• 空格: 暂停/继续</p>
        <p>• R: 重置系统</p>
        <p><strong>三体系统:</strong></p>
        <p>• 红色恒星 (质量: 100)</p>
        <p>• 蓝色恒星 (质量: 120)</p>
        <p>• 黄色恒星 (质量: 110)</p>
        <p>• 绿色行星在混沌轨道中运行</p>
    </div>

    <script>
        // 全局变量
        var stars = [];
        var planet;
        var particles = [];
        var starField = [];
        var camera = {
            angleX: 0,
            angleY: 0,
            distance: 800,
            targetDistance: 800
        };
        var timeSpeed = 1.0;
        var isPaused = false;
        var glowIntensity = 1.5;

        // 物理常数
        var G = 0.4;
        var dt = 0.015;

        // 恒星类
        function Star(x, y, z, mass, color, size) {
            this.pos = createVector(x, y, z);
            this.vel = createVector(0, 0, 0);
            this.mass = mass;
            this.color = color;
            this.size = size;
            this.trail = [];
            this.glowParticles = [];

            this.update = function(otherStars) {
                if (isPaused) return;

                var force = createVector(0, 0, 0);

                for (var i = 0; i < otherStars.length; i++) {
                    var other = otherStars[i];
                    if (other !== this) {
                        var r = p5.Vector.sub(other.pos, this.pos);
                        var distance = r.mag();
                        distance = max(distance, 15);

                        var forceMag = G * this.mass * other.mass / (distance * distance);
                        r.normalize();
                        r.mult(forceMag);
                        force.add(r);
                    }
                }

                var acc = p5.Vector.div(force, this.mass);
                this.vel.add(p5.Vector.mult(acc, dt * timeSpeed));
                this.pos.add(p5.Vector.mult(this.vel, dt * timeSpeed));

                // 更新轨迹
                this.trail.push(this.pos.copy());
                if (this.trail.length > 300) {
                    this.trail.shift();
                }

                // 更新发光粒子
                this.updateGlowParticles();
            };

            this.updateGlowParticles = function() {
                if (frameCount % 2 === 0) {
                    for (var i = 0; i < 2; i++) {
                        this.glowParticles.push({
                            pos: this.pos.copy(),
                            vel: p5.Vector.random3D().mult(random(0.5, 2)),
                            life: 1.0,
                            size: random(1, 4)
                        });
                    }
                }

                for (var i = this.glowParticles.length - 1; i >= 0; i--) {
                    var p = this.glowParticles[i];
                    p.pos.add(p.vel);
                    p.life -= 0.015;
                    p.size *= 0.99;

                    if (p.life <= 0) {
                        this.glowParticles.splice(i, 1);
                    }
                }
            };

            this.display = function() {
                push();
                translate(this.pos.x, this.pos.y, this.pos.z);

                // 多层发光效果
                for (var i = 0; i < 6; i++) {
                    var alpha = map(i, 0, 5, 200 * glowIntensity, 20);
                    var size = this.size + i * 6 * glowIntensity;
                    fill(red(this.color), green(this.color), blue(this.color), alpha);
                    noStroke();
                    sphere(size);
                }

        // 粒子类
        function Particle() {
            this.pos = createVector(
                random(-1500, 1500),
                random(-1500, 1500),
                random(-1500, 1500)
            );
            this.vel = p5.Vector.random3D().mult(random(0.1, 0.3));
            this.size = random(0.5, 2.5);
            this.brightness = random(80, 255);
            this.twinkle = random(0, TWO_PI);
            this.twinkleSpeed = random(0.02, 0.08);

            this.update = function() {
                if (!isPaused) {
                    this.pos.add(this.vel);
                    this.twinkle += this.twinkleSpeed;

                    // 边界循环
                    if (this.pos.x > 1500) this.pos.x = -1500;
                    if (this.pos.x < -1500) this.pos.x = 1500;
                    if (this.pos.y > 1500) this.pos.y = -1500;
                    if (this.pos.y < -1500) this.pos.y = 1500;
                    if (this.pos.z > 1500) this.pos.z = -1500;
                    if (this.pos.z < -1500) this.pos.z = 1500;
                }
            };

            this.display = function() {
                push();
                translate(this.pos.x, this.pos.y, this.pos.z);
                var alpha = this.brightness + sin(this.twinkle) * 80;
                fill(255, 255, 255, alpha);
                noStroke();
                sphere(this.size);
                pop();
            };
        }

        // 创建星空背景
        function createStarField() {
            starField = [];
            for (var i = 0; i < 300; i++) {
                starField.push({
                    x: random(-2500, 2500),
                    y: random(-2500, 2500),
                    z: random(-2500, 2500),
                    size: random(0.5, 3),
                    brightness: random(100, 255),
                    color: random(['white', 'blue', 'yellow', 'red'])
                });
            }
        }

        // 绘制星空背景
        function drawStarField() {
            for (var i = 0; i < starField.length; i++) {
                var star = starField[i];
                push();
                translate(star.x, star.y, star.z);

                switch(star.color) {
                    case 'blue':
                        fill(150, 200, 255, star.brightness);
                        break;
                    case 'yellow':
                        fill(255, 255, 150, star.brightness);
                        break;
                    case 'red':
                        fill(255, 150, 150, star.brightness);
                        break;
                    default:
                        fill(255, 255, 255, star.brightness);
                }

                noStroke();
                sphere(star.size);
                pop();
            }
        }

        // p5.js 主函数
        function setup() {
            createCanvas(windowWidth, windowHeight, WEBGL);

            // 初始化三个恒星
            stars.push(new Star(-180, 0, 0, 100, color(255, 80, 80), 20));   // 红色恒星
            stars.push(new Star(180, 0, 0, 120, color(80, 150, 255), 22));   // 蓝色恒星
            stars.push(new Star(0, 180, 0, 110, color(255, 255, 80), 21));   // 黄色恒星

            // 给恒星初始速度
            stars[0].vel = createVector(0, 0.4, 0.15);
            stars[1].vel = createVector(0, -0.25, -0.35);
            stars[2].vel = createVector(0.35, -0.15, 0.1);

            // 初始化行星
            planet = new Planet(120, -80, 40);
            planet.vel = createVector(0.6, 0.5, 0.25);

            // 初始化粒子
            for (var i = 0; i < 200; i++) {
                particles.push(new Particle());
            }

            // 初始化星空背景
            createStarField();

            // 设置控制器
            setupControls();
        }

        function draw() {
            background(2, 2, 8);

            // 更新相机
            updateCamera();

            // 设置光照
            ambientLight(25, 25, 35);
            pointLight(255, 255, 255, 0, 0, 0);

            // 绘制星空背景
            drawStarField();

            // 更新和绘制粒子
            for (var i = 0; i < particles.length; i++) {
                particles[i].update();
                particles[i].display();
            }

            // 更新和绘制恒星
            for (var i = 0; i < stars.length; i++) {
                stars[i].update(stars);
                stars[i].display();
            }

            // 更新和绘制行星
            planet.update(stars);
            planet.display();

            // 更新状态显示
            updateStatusDisplay();
        }

        // 相机控制
        function updateCamera() {
            camera.distance = lerp(camera.distance, camera.targetDistance, 0.05);

            var x = camera.distance * cos(camera.angleY) * cos(camera.angleX);
            var y = camera.distance * sin(camera.angleX);
            var z = camera.distance * sin(camera.angleY) * cos(camera.angleX);

            camera(x, y, z, 0, 0, 0, 0, 1, 0);
        }

        // 设置控制器
        function setupControls() {
            // 时间流速控制
            document.getElementById('timeSpeed').addEventListener('input', function(e) {
                timeSpeed = parseFloat(e.target.value);
                document.getElementById('timeSpeedValue').textContent = timeSpeed.toFixed(1) + 'x';
            });

            // 相机距离控制
            document.getElementById('cameraDistance').addEventListener('input', function(e) {
                camera.targetDistance = parseInt(e.target.value);
                document.getElementById('cameraDistanceValue').textContent = e.target.value;
            });

            // 粒子密度控制
            document.getElementById('particleCount').addEventListener('input', function(e) {
                var newCount = parseInt(e.target.value);
                var currentCount = particles.length;

                if (newCount > currentCount) {
                    for (var i = 0; i < newCount - currentCount; i++) {
                        particles.push(new Particle());
                    }
                } else if (newCount < currentCount) {
                    particles.splice(newCount);
                }

                document.getElementById('particleCountValue').textContent = newCount;
            });

            // 发光强度控制
            document.getElementById('glowIntensity').addEventListener('input', function(e) {
                glowIntensity = parseFloat(e.target.value);
                document.getElementById('glowIntensityValue').textContent = glowIntensity.toFixed(1);
            });
        }

        // 更新状态显示
        function updateStatusDisplay() {
            if (frameCount % 30 === 0) {
                document.getElementById('fps-display').textContent = frameRate().toFixed(1);
                document.getElementById('status-display').textContent = isPaused ? '已暂停' : '运行中';
            }
        }

        // 鼠标交互
        function mouseDragged() {
            if (mouseX > 0 && mouseX < width && mouseY > 0 && mouseY < height) {
                camera.angleY += (mouseX - pmouseX) * 0.008;
                camera.angleX -= (mouseY - pmouseY) * 0.008;
                camera.angleX = constrain(camera.angleX, -PI/2, PI/2);
            }
        }

        function mouseWheel(event) {
            camera.targetDistance += event.delta * 3;
            camera.targetDistance = constrain(camera.targetDistance, 200, 2500);
            document.getElementById('cameraDistance').value = camera.targetDistance;
            document.getElementById('cameraDistanceValue').textContent = camera.targetDistance;
            return false;
        }

        // 键盘控制
        function keyPressed() {
            if (key === ' ') {
                togglePause();
            } else if (key === 'r' || key === 'R') {
                resetSystem();
            }
        }

        // 暂停/继续
        function togglePause() {
            isPaused = !isPaused;
        }

        // 重置系统
        function resetSystem() {
            // 重置恒星
            stars[0].pos = createVector(-180, 0, 0);
            stars[0].vel = createVector(0, 0.4, 0.15);
            stars[0].trail = [];
            stars[0].glowParticles = [];

            stars[1].pos = createVector(180, 0, 0);
            stars[1].vel = createVector(0, -0.25, -0.35);
            stars[1].trail = [];
            stars[1].glowParticles = [];

            stars[2].pos = createVector(0, 180, 0);
            stars[2].vel = createVector(0.35, -0.15, 0.1);
            stars[2].trail = [];
            stars[2].glowParticles = [];

            // 重置行星
            planet.pos = createVector(120, -80, 40);
            planet.vel = createVector(0.6, 0.5, 0.25);
            planet.trail = [];

            // 重置相机
            camera.angleX = 0;
            camera.angleY = 0;
            camera.targetDistance = 800;

            isPaused = false;
        }

        // 窗口大小调整
        function windowResized() {
            resizeCanvas(windowWidth, windowHeight);
        }
    </script>
</body>
</html>
