<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>动态天气卡片</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background: linear-gradient(135deg, #1e3c72, #2a5298);
            min-height: 100vh;
            font-family: 'Arial', sans-serif;
            color: white;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
        }

        .weather-controls {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-bottom: 40px;
            flex-wrap: wrap;
        }

        .weather-btn {
            padding: 12px 24px;
            border: none;
            border-radius: 25px;
            background: rgba(255,255,255,0.1);
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 1rem;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
        }

        .weather-btn:hover {
            background: rgba(255,255,255,0.2);
            transform: translateY(-2px);
        }

        .weather-btn.active {
            background: rgba(255,255,255,0.3);
            box-shadow: 0 4px 15px rgba(255,255,255,0.2);
        }

        .weather-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 30px;
            margin-top: 30px;
        }

        .weather-card {
            background: rgba(255,255,255,0.1);
            border-radius: 20px;
            padding: 30px;
            text-align: center;
            position: relative;
            overflow: hidden;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
            height: 300px;
            transition: all 0.3s ease;
        }

        .weather-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }

        .weather-icon {
            font-size: 4rem;
            margin-bottom: 20px;
            display: block;
        }

        .weather-name {
            font-size: 1.5rem;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .weather-desc {
            font-size: 1rem;
            opacity: 0.8;
        }

        /* 大风动画 */
        .wind-card {
            background: linear-gradient(135deg, #74b9ff, #0984e3);
        }

        .wind-lines {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
        }

        .wind-line {
            position: absolute;
            width: 60px;
            height: 2px;
            background: rgba(255,255,255,0.6);
            border-radius: 1px;
        }

        .wind-line:nth-child(1) {
            top: 20%;
            left: -60px;
            animation: windMove1 3s infinite linear;
        }

        .wind-line:nth-child(2) {
            top: 40%;
            left: -60px;
            animation: windMove2 2.5s infinite linear;
        }

        .wind-line:nth-child(3) {
            top: 60%;
            left: -60px;
            animation: windMove1 3.5s infinite linear;
        }

        @keyframes windMove1 {
            0% { left: -60px; }
            100% { left: 100%; }
        }

        @keyframes windMove2 {
            0% { left: -60px; }
            100% { left: 100%; }
        }

        /* 雨天动画 */
        .rain-card {
            background: linear-gradient(135deg, #636e72, #2d3436);
        }

        .rain-drops {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
        }

        .rain-drop {
            position: absolute;
            width: 2px;
            height: 20px;
            background: rgba(173, 216, 230, 0.8);
            border-radius: 1px;
        }

        .rain-drop:nth-child(1) {
            left: 20%;
            animation: rainFall 1s infinite linear;
        }

        .rain-drop:nth-child(2) {
            left: 40%;
            animation: rainFall 1.2s infinite linear;
            animation-delay: 0.2s;
        }

        .rain-drop:nth-child(3) {
            left: 60%;
            animation: rainFall 0.8s infinite linear;
            animation-delay: 0.4s;
        }

        .rain-drop:nth-child(4) {
            left: 80%;
            animation: rainFall 1.1s infinite linear;
            animation-delay: 0.6s;
        }

        @keyframes rainFall {
            0% { top: -20px; opacity: 1; }
            100% { top: 100%; opacity: 0; }
        }

        /* 晴天动画 */
        .sunny-card {
            background: linear-gradient(135deg, #fdcb6e, #e17055);
        }

        .sun-rays {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 100px;
            height: 100px;
            animation: sunRotate 4s infinite linear;
        }

        .sun-rays::before {
            content: '';
            position: absolute;
            top: -20px;
            left: 50%;
            transform: translateX(-50%);
            width: 4px;
            height: 40px;
            background: rgba(255,255,255,0.8);
            border-radius: 2px;
            box-shadow: 
                0 0 0 0 rgba(255,255,255,0.8),
                30px 30px 0 0 rgba(255,255,255,0.8),
                42px 0 0 0 rgba(255,255,255,0.8),
                30px -30px 0 0 rgba(255,255,255,0.8),
                0 -42px 0 0 rgba(255,255,255,0.8),
                -30px -30px 0 0 rgba(255,255,255,0.8),
                -42px 0 0 0 rgba(255,255,255,0.8),
                -30px 30px 0 0 rgba(255,255,255,0.8);
        }

        @keyframes sunRotate {
            0% { transform: translate(-50%, -50%) rotate(0deg); }
            100% { transform: translate(-50%, -50%) rotate(360deg); }
        }

        /* 雪天动画 */
        .snow-card {
            background: linear-gradient(135deg, #ddd6fe, #8b5cf6);
        }

        .snowflakes {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
        }

        .snowflake {
            position: absolute;
            color: white;
            font-size: 1rem;
            animation: snowFall 3s infinite linear;
        }

        .snowflake:nth-child(1) {
            left: 10%;
            animation-duration: 3s;
            animation-delay: 0s;
        }

        .snowflake:nth-child(2) {
            left: 30%;
            animation-duration: 2.5s;
            animation-delay: 0.5s;
        }

        .snowflake:nth-child(3) {
            left: 50%;
            animation-duration: 3.5s;
            animation-delay: 1s;
        }

        .snowflake:nth-child(4) {
            left: 70%;
            animation-duration: 2.8s;
            animation-delay: 1.5s;
        }

        .snowflake:nth-child(5) {
            left: 90%;
            animation-duration: 3.2s;
            animation-delay: 2s;
        }

        @keyframes snowFall {
            0% {
                top: -10px;
                transform: translateX(0px) rotate(0deg);
                opacity: 1;
            }
            100% {
                top: 100%;
                transform: translateX(50px) rotate(360deg);
                opacity: 0;
            }
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .weather-cards {
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                gap: 20px;
            }
            
            .weather-card {
                height: 250px;
                padding: 20px;
            }
            
            .weather-icon {
                font-size: 3rem;
            }
            
            .header h1 {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🌤️ 动态天气卡片</h1>
            <div class="weather-controls">
                <button class="weather-btn active" data-weather="all">全部显示</button>
                <button class="weather-btn" data-weather="wind">大风</button>
                <button class="weather-btn" data-weather="rain">雨天</button>
                <button class="weather-btn" data-weather="sunny">晴天</button>
                <button class="weather-btn" data-weather="snow">雪天</button>
            </div>
        </div>

        <div class="weather-cards">
            <!-- 大风卡片 -->
            <div class="weather-card wind-card" data-type="wind">
                <div class="weather-icon">💨</div>
                <div class="weather-name">大风</div>
                <div class="weather-desc">风力强劲，注意安全</div>
                <div class="wind-lines">
                    <div class="wind-line"></div>
                    <div class="wind-line"></div>
                    <div class="wind-line"></div>
                </div>
            </div>

            <!-- 雨天卡片 -->
            <div class="weather-card rain-card" data-type="rain">
                <div class="weather-icon">🌧️</div>
                <div class="weather-name">雨天</div>
                <div class="weather-desc">雨水连绵，记得带伞</div>
                <div class="rain-drops">
                    <div class="rain-drop"></div>
                    <div class="rain-drop"></div>
                    <div class="rain-drop"></div>
                    <div class="rain-drop"></div>
                </div>
            </div>

            <!-- 晴天卡片 -->
            <div class="weather-card sunny-card" data-type="sunny">
                <div class="weather-icon">☀️</div>
                <div class="weather-name">晴天</div>
                <div class="weather-desc">阳光明媚，适合出行</div>
                <div class="sun-rays"></div>
            </div>

            <!-- 雪天卡片 -->
            <div class="weather-card snow-card" data-type="snow">
                <div class="weather-icon">❄️</div>
                <div class="weather-name">雪天</div>
                <div class="weather-desc">雪花飞舞，银装素裹</div>
                <div class="snowflakes">
                    <div class="snowflake">❄</div>
                    <div class="snowflake">❅</div>
                    <div class="snowflake">❄</div>
                    <div class="snowflake">❅</div>
                    <div class="snowflake">❄</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 天气切换功能
        document.addEventListener('DOMContentLoaded', function() {
            const weatherBtns = document.querySelectorAll('.weather-btn');
            const weatherCards = document.querySelectorAll('.weather-card');

            weatherBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    const selectedWeather = this.getAttribute('data-weather');
                    
                    // 更新按钮状态
                    weatherBtns.forEach(b => b.classList.remove('active'));
                    this.classList.add('active');
                    
                    // 显示/隐藏卡片
                    weatherCards.forEach(card => {
                        const cardType = card.getAttribute('data-type');
                        
                        if (selectedWeather === 'all' || selectedWeather === cardType) {
                            card.style.display = 'block';
                            card.style.opacity = '1';
                            card.style.transform = 'translateY(0)';
                        } else {
                            card.style.opacity = '0';
                            card.style.transform = 'translateY(20px)';
                            setTimeout(() => {
                                if (card.style.opacity === '0') {
                                    card.style.display = 'none';
                                }
                            }, 300);
                        }
                    });
                });
            });

            // 初始化动画
            function initAnimations() {
                // 为每个卡片添加悬停效果
                weatherCards.forEach(card => {
                    card.addEventListener('mouseenter', function() {
                        this.style.transform = 'translateY(-10px) scale(1.02)';
                    });
                    
                    card.addEventListener('mouseleave', function() {
                        this.style.transform = 'translateY(0) scale(1)';
                    });
                });
            }

            initAnimations();
        });
    </script>
</body>
</html>
