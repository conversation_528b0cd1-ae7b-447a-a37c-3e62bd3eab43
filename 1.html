<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>动态天气卡片</title>
    <style>
        /* General page styling */
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
            background-color: #1a1a2e; /* Dark blueish background */
            color: #e0e0e0;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
            margin: 0;
            padding: 20px;
            box-sizing: border-box;
        }

        /* Controls styling */
        .controls {
            margin-bottom: 30px;
            display: flex;
            gap: 10px;
            flex-wrap: wrap; /* Allow buttons to wrap on smaller screens */
            justify-content: center;
        }

        .controls button {
            background-color: #4a4a70; /* Darker button color */
            color: #e0e0e0;
            border: none;
            padding: 12px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            transition: background-color 0.3s ease, transform 0.2s ease;
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }

        .controls button:hover {
            background-color: #6a6a90; /* Lighter hover */
        }

        .controls button.active-btn {
            background-color: #8a8ac0; /* Active button color */
            color: #1a1a2e;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.3);
        }

        /* Weather cards container */
        .weather-cards-container {
            display: flex;
            flex-wrap: wrap; /* Allow cards to wrap on smaller screens */
            gap: 20px;
            justify-content: center;
            perspective: 1000px; /* For 3D effects if any */
        }

        /* Individual weather card styling */
        .weather-card {
            width: 220px;
            height: 320px;
            background-color: #2c2c54; /* Dark card background */
            border-radius: 15px;
            padding: 20px;
            box-sizing: border-box;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: space-between; /* Pushes title to top, elements to center/bottom */
            position: relative;
            overflow: hidden; /* Crucial for containing animated elements */
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .weather-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 20px rgba(0,0,0,0.4);
        }
        
        .card-title {
            font-size: 20px;
            font-weight: bold;
            text-align: center;
            color: #f0f0f0;
            z-index: 10; /* Ensure title is above animations */
            background-color: rgba(0,0,0,0.3);
            padding: 5px 10px;
            border-radius: 5px;
        }

        .animation-area {
            width: 100%;
            height: 100%; /* Take full card space for animations */
            position: absolute;
            top: 0;
            left: 0;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }

        /* --- Windy Animations --- */
        .windy-card .cloud {
            position: absolute;
            background-color: #b0c4de; /* Light steel blue for clouds */
            border-radius: 50%;
            opacity: 0.8;
        }
        .windy-card .cloud.c1 { width: 60px; height: 30px; top: 20%; left: -70px; }
        .windy-card .cloud.c2 { width: 80px; height: 40px; top: 35%; left: -90px; }
        .windy-card .cloud.c3 { width: 50px; height: 25px; top: 50%; left: -60px; }

        .windy-card.active .cloud.c1 { animation: moveCloud1 8s linear infinite 0s; }
        .windy-card.active .cloud.c2 { animation: moveCloud2 10s linear infinite 0.5s; }
        .windy-card.active .cloud.c3 { animation: moveCloud1 12s linear infinite 1s; }

        @keyframes moveCloud1 {
            0% { transform: translateX(0); }
            100% { transform: translateX(300px); } /* Card width + cloud width */
        }
        @keyframes moveCloud2 {
            0% { transform: translateX(0); }
            100% { transform: translateX(320px); }
        }

        .windy-card .tree { position: absolute; bottom: 20px; left: 50%; transform: translateX(-50%); width: 40px; height: 100px; }
        .windy-card .trunk { width: 10px; height: 60px; background-color: #8B4513; /* SaddleBrown */ margin: 0 auto; position: absolute; bottom: 0; left: 15px;}
        .windy-card .canopy { width: 40px; height: 50px; background-color: #2E8B57; /* SeaGreen */ border-radius: 50% 50% 20% 20%; position: absolute; top: 0; }
        .windy-card.active .canopy { animation: swayTree 1.5s ease-in-out infinite alternate; transform-origin: bottom center; }
        @keyframes swayTree {
            0% { transform: rotate(-5deg); }
            100% { transform: rotate(5deg); }
        }

        .windy-card .wind-line {
            position: absolute;
            height: 2px;
            background-color: rgba(200, 200, 255, 0.6);
            border-radius: 1px;
        }
        .windy-card .wl1 { width: 30px; top: 40%; left: -40px; }
        .windy-card .wl2 { width: 40px; top: 60%; left: -50px; transform: translateY(10px); }
        .windy-card .wl3 { width: 25px; top: 75%; left: -35px; transform: translateY(5px); }
        .windy-card.active .wl1 { animation: blowWind 0.8s linear infinite 0.1s; }
        .windy-card.active .wl2 { animation: blowWind 0.7s linear infinite 0.3s; }
        .windy-card.active .wl3 { animation: blowWind 0.9s linear infinite 0.2s; }
        @keyframes blowWind {
            0% { transform: translateX(0px) scaleX(0.5); opacity: 0; }
            20% { opacity: 1; transform: scaleX(1); }
            80% { opacity: 1; }
            100% { transform: translateX(250px) scaleX(0.5); opacity: 0; }
        }

        /* --- Rainy Animations --- */
        .rainy-card .raindrop {
            position: absolute;
            width: 2px;
            height: 15px;
            background-color: #87CEFA; /* LightSkyBlue */
            border-radius: 1px;
            opacity: 0;
        }
        .rainy-card.active .raindrop { animation: fallRain 0.8s linear infinite; }
        /* Stagger raindrop animations using animation-delay */
        .rainy-card .r1 { top: -20px; left: 20%; animation-delay: 0s; }
        .rainy-card .r2 { top: -20px; left: 40%; animation-delay: 0.3s; }
        .rainy-card .r3 { top: -20px; left: 60%; animation-delay: 0.1s; }
        .rainy-card .r4 { top: -20px; left: 80%; animation-delay: 0.5s; }
        .rainy-card .r5 { top: -20px; left: 30%; animation-delay: 0.2s; }
        .rainy-card .r6 { top: -20px; left: 70%; animation-delay: 0.4s; }
        .rainy-card .r7 { top: -20px; left: 10%; animation-delay: 0.6s; }
        .rainy-card .r8 { top: -20px; left: 50%; animation-delay: 0.05s; }
        .rainy-card .r9 { top: -20px; left: 90%; animation-delay: 0.25s; }


        @keyframes fallRain {
            0% { transform: translateY(0); opacity: 0; }
            10% { opacity: 1; }
            90% { opacity: 1; }
            100% { transform: translateY(320px); opacity: 0; } /* Card height */
        }

        .rainy-card .puddle-effect {
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 0px;
            background-color: rgba(135, 206, 250, 0.3); /* LightSkyBlue with alpha */
            border-top-left-radius: 50% 10px;
            border-top-right-radius: 50% 10px;
        }
        .rainy-card.active .puddle-effect { animation: accumulateWater 5s forwards 1s; }
        @keyframes accumulateWater {
            0% { height: 0px; opacity: 0.5;}
            100% { height: 30px; opacity: 1;}
        }

        /* --- Sunny Animations --- */
        .sunny-card { background-color: #FFD700; /* Gold - default bright for sunny */ }
        .sunny-card.active { background: linear-gradient(180deg, #FFEC8B 0%, #FFD700 100%); } /* Lighter gold gradient */
        .sunny-card .card-title { color: #4A3B00; background-color: rgba(255,255,255,0.4); }

        .sunny-card .sun-disc {
            width: 80px;
            height: 80px;
            background-color: #FFFACD; /* LemonChiffon */
            border-radius: 50%;
            position: absolute;
            top: 30%;
            left: 50%;
            transform: translate(-50%, -50%);
            box-shadow: 0 0 20px #FFFACD, 0 0 40px #FFD700, 0 0 60px #FFA500;
        }
        .sunny-card.active .sun-disc { animation: pulseSun 2s infinite ease-in-out; }
        @keyframes pulseSun {
            0% { transform: translate(-50%, -50%) scale(1); box-shadow: 0 0 20px #FFFACD, 0 0 40px #FFD700, 0 0 60px #FFA500; }
            50% { transform: translate(-50%, -50%) scale(1.1); box-shadow: 0 0 30px #FFFACD, 0 0 50px #FFD700, 0 0 70px #FFA500; }
            100% { transform: translate(-50%, -50%) scale(1); box-shadow: 0 0 20px #FFFACD, 0 0 40px #FFD700, 0 0 60px #FFA500; }
        }
        
        .sunny-card .sun-beams {
            width: 150px;
            height: 150px;
            position: absolute;
            top: 30%;
            left: 50%;
            transform: translate(-50%, -50%);
        }
        .sunny-card.active .sun-beams::before,
        .sunny-card.active .sun-beams::after {
            content: '';
            position: absolute;
            top: 0; left: 0; right: 0; bottom: 0;
            background: radial-gradient(circle, rgba(255,235,155,0.5) 20%, transparent 70%);
            border-radius: 50%;
            animation: rotateBeams 20s linear infinite;
        }
        .sunny-card.active .sun-beams::after {
            animation-delay: -10s; /* Offset animation */
            transform: scale(0.8);
        }

        @keyframes rotateBeams {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        /* --- Snowy Animations --- */
        .snowy-card .snowflake {
            position: absolute;
            width: 8px;
            height: 8px;
            background-color: #FFFFFF;
            border-radius: 50%;
            opacity: 0;
        }
        .snowy-card.active .snowflake { animation: fallSnow 8s linear infinite; }

        /* Stagger snowflake animations */
        .snowy-card .sf1 { top: -10px; left: 10%; width:6px; height:6px; animation-duration: 10s; animation-delay: 0s; }
        .snowy-card .sf2 { top: -10px; left: 30%; width:8px; height:8px; animation-duration: 8s;  animation-delay: 1.5s; }
        .snowy-card .sf3 { top: -10px; left: 50%; width:5px; height:5px; animation-duration: 12s; animation-delay: 0.5s; }
        .snowy-card .sf4 { top: -10px; left: 70%; width:7px; height:7px; animation-duration: 9s;  animation-delay: 2.5s; }
        .snowy-card .sf5 { top: -10px; left: 90%; width:6px; height:6px; animation-duration: 11s; animation-delay: 1s; }
        .snowy-card .sf6 { top: -10px; left: 20%; width:8px; height:8px; animation-duration: 7s;  animation-delay: 3s; }
        .snowy-card .sf7 { top: -10px; left: 40%; width:5px; height:5px; animation-duration: 13s; animation-delay: 0.2s; }
        .snowy-card .sf8 { top: -10px; left: 60%; width:7px; height:7px; animation-duration: 10s; animation-delay: 2s; }
        .snowy-card .sf9 { top: -10px; left: 80%; width:6px; height:6px; animation-duration: 8s;  animation-delay: 1.2s; }


        @keyframes fallSnow {
            0% { transform: translateY(0) translateX(0px) rotate(0deg); opacity: 0; }
            10% { opacity: 0.9; }
            90% { opacity: 0.9; }
            100% { transform: translateY(330px) translateX(20px) rotate(360deg); opacity: 0; } /* Card height + drift */
        }

        .snowy-card .snow-accumulation {
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 0px;
            background-color: rgba(240, 248, 255, 0.8); /* AliceBlue with alpha */
            border-top-left-radius: 50% 20px;
            border-top-right-radius: 50% 20px;
            box-shadow: 0 -5px 10px rgba(255,255,255,0.3);
        }
        .snowy-card.active .snow-accumulation { animation: accumulateSnow 8s forwards 1s; }
        @keyframes accumulateSnow {
            0% { height: 0px; opacity: 0.7;}
            100% { height: 50px; opacity: 1;}
        }
        
        /* Responsive adjustments */
        @media (max-width: 1000px) {
            .weather-cards-container {
                flex-direction: column; /* Stack cards vertically on smaller screens */
                align-items: center;
            }
            .weather-card {
                width: 280px; /* Slightly wider cards when stacked */
                height: 350px;
            }
             .controls {
                flex-direction: column;
                align-items: stretch; /* Make buttons full width of their container */
            }
            .controls button {
                width: 100%;
                margin-bottom: 5px; /* Space between stacked buttons */
            }
        }
        @media (max-width: 480px) {
             .weather-card {
                width: 90vw; /* Make cards take most of viewport width */
                height: auto; /* Adjust height automatically */
                min-height: 300px;
            }
            .card-title {
                font-size: 18px;
            }
        }


    </style>
</head>
<body>

    <div class="controls">
        <button data-weather="windy">大风</button>
        <button data-weather="rainy">雨天</button>
        <button data-weather="sunny">晴天</button>
        <button data-weather="snowy">雪天</button>
    </div>

    <div class="weather-cards-container">
        <div class="weather-card" id="windy-card">
            <div class="card-title">大风</div>
            <div class="animation-area">
                <div class="cloud c1"></div>
                <div class="cloud c2"></div>
                <div class="cloud c3"></div>
                <div class="tree">
                    <div class="trunk"></div>
                    <div class="canopy"></div>
                </div>
                <div class="wind-lines-container"> <div class="wind-line wl1"></div>
                    <div class="wind-line wl2"></div>
                    <div class="wind-line wl3"></div>
                </div>
            </div>
        </div>

        <div class="weather-card" id="rainy-card">
            <div class="card-title">雨天</div>
            <div class="animation-area">
                <div class="raindrop r1"></div>
                <div class="raindrop r2"></div>
                <div class="raindrop r3"></div>
                <div class="raindrop r4"></div>
                <div class="raindrop r5"></div>
                <div class="raindrop r6"></div>
                <div class="raindrop r7"></div>
                <div class="raindrop r8"></div>
                <div class="raindrop r9"></div>
                <div class="puddle-effect"></div>
            </div>
        </div>

        <div class="weather-card" id="sunny-card">
            <div class="card-title">晴天</div>
            <div class="animation-area">
                <div class="sun-beams"></div>
                <div class="sun-disc"></div>
            </div>
        </div>

        <div class="weather-card" id="snowy-card">
            <div class="card-title">雪天</div>
            <div class="animation-area">
                <div class="snowflake sf1"></div>
                <div class="snowflake sf2"></div>
                <div class="snowflake sf3"></div>
                <div class="snowflake sf4"></div>
                <div class="snowflake sf5"></div>
                <div class="snowflake sf6"></div>
                <div class="snowflake sf7"></div>
                <div class="snowflake sf8"></div>
                <div class="snowflake sf9"></div>
                <div class="snow-accumulation"></div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const buttons = document.querySelectorAll('.controls button');
            const cards = document.querySelectorAll('.weather-card');
            const defaultActiveWeather = 'sunny'; // Set Sunny as default active

            function setActiveWeather(weatherType) {
                cards.forEach(card => {
                    // Reset accumulation effects by removing and re-adding the element or class
                    // For simplicity, CSS 'forwards' will keep accumulation.
                    // To reset, we'd need to clone/replace or toggle animation names.
                    // For now, accumulation will persist until page reload for non-active then re-active.
                    // A better reset would involve more complex JS to restart 'forwards' animations.
                    
                    // Remove 'active' class from all cards
                    card.classList.remove('active');
                    
                    // If card has puddle or snow, reset them by briefly hiding/showing or re-triggering animation
                    // This is a simplified reset. A full reset of 'forwards' animations is more involved.
                    const puddle = card.querySelector('.puddle-effect');
                    const snow = card.querySelector('.snow-accumulation');
                    if (puddle) puddle.style.animation = 'none'; // Stop animation
                    if (snow) snow.style.animation = 'none'; // Stop animation

                    // Force reflow to allow animation restart
                    if (puddle) void puddle.offsetWidth; 
                    if (snow) void snow.offsetWidth;
                });

                buttons.forEach(button => {
                    button.classList.remove('active-btn');
                });

                const selectedCard = document.getElementById(`${weatherType}-card`);
                const selectedButton = document.querySelector(`.controls button[data-weather="${weatherType}"]`);

                if (selectedCard) {
                    selectedCard.classList.add('active');
                    // Re-apply animations for accumulation effects if they exist
                    const puddle = selectedCard.querySelector('.puddle-effect');
                    const snow = selectedCard.querySelector('.snow-accumulation');
                    if (puddle) puddle.style.animation = ''; // Re-apply CSS defined animation
                    if (snow) snow.style.animation = ''; // Re-apply CSS defined animation
                }
                if (selectedButton) {
                    selectedButton.classList.add('active-btn');
                }
            }

            buttons.forEach(button => {
                button.addEventListener('click', () => {
                    const weatherType = button.dataset.weather;
                    setActiveWeather(weatherType);
                });
            });

            // Set the default active weather card on page load
            setActiveWeather(defaultActiveWeather);
        });
    </script>

</body>
</html>
